-- core/message_processor.lua
-- 消息预处理模块：处理消息解析、@检测、昵称匹配等

local MessageProcessor = {}

-- 配置
local BotConfig = {
    KeepAtMeMessage = false,  -- 是否保留@机器人的消息段
    NickName = {"小猫", "猫猫", "bot"}  -- 机器人昵称列表
}

-- 解析消息段（简化版，根据实际OneBot消息格式调整）
local function parseMessage(raw_message)
    -- 这里需要根据实际的OneBot消息格式来解析
    -- 暂时返回简单的文本消息段
    return {{type = "text", data = {text = raw_message}}}
end

-- 预处理消息事件
function MessageProcessor.preprocessMessageEvent(event, self_id)
    -- 解析消息
    local msgs = parseMessage(event.raw_message or "")
    
    if #msgs > 0 then
        local filtered = {}
        -- 处理@后的空格并移除空文本段
        for i, msg in ipairs(msgs) do
            if i < #msgs and msg.type == "at" and msgs[i+1].type == "text" then
                msgs[i+1].data.text = msgs[i+1].data.text:gsub("^%s+", "")
            end
            if msg.type ~= "text" or (msg.data.text and msg.data.text ~= "") then
                table.insert(filtered, msg)
            end
        end
        event.message = filtered
    else
        event.message = {}
    end
    
    -- 处理@机器人检测
    local function processAt()
        event.is_to_me = false
        if #event.message == 0 then return end
        
        for i, m in ipairs(event.message) do
            if m.type == "at" and tonumber(m.data.qq) == self_id then
                event.is_to_me = true
                if not BotConfig.KeepAtMeMessage then
                    table.remove(event.message, i)
                end
                return
            end
        end
        
        -- 检查昵称匹配
        if #event.message == 0 or event.message[1].type ~= "text" then
            return
        end
        
        local first = event.message[1]
        first.data.text = first.data.text:gsub("^%s+", "")
        local text = first.data.text
        
        for _, nickname in ipairs(BotConfig.NickName) do
            if text:sub(1, #nickname) == nickname then
                event.is_to_me = true
                first.data.text = text:sub(#nickname + 1)
                return
            end
        end
    end
    
    -- 根据消息类型处理
    if event.message_type == "group" then
        print(string.format("[bot] 收到群(%s)消息 %s : %s", 
            event.group_id, event.sender.nickname or event.user_id, event.raw_message))
        processAt()
    elseif event.message_type == "guild" and event.sub_type == "channel" then
        print(string.format("[bot] 收到频道(%s)(%s-%s)消息 %s : %s", 
            event.group_id, event.guild_id, event.channel_id, 
            event.sender.nickname or event.user_id, event.raw_message))
        processAt()
    else
        event.is_to_me = true  -- 私聊默认为@
        print(string.format("[bot] 收到私聊消息 %s : %s", 
            event.sender.nickname or event.user_id, event.raw_message))
    end
    
    -- 最后再次清理首个文本段的空格
    if #event.message > 0 and event.message[1].type == "text" then
        event.message[1].data.text = event.message[1].data.text:gsub("^%s+", "")
    end
end

return MessageProcessor