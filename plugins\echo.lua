-- plugins/echo.lua
return function(bot)
    -- bot 对象是从 core/bot.lua 传进来的

    -- 监听群消息事件
    bot:on('message.group', function(event)
        -- event 是从 OneBot 接收到的完整事件对象
        print(string.format("[Plugin Echo] 收到群消息: %d: %s", event.group_id, event.raw_message))

        if event.raw_message == "你好" then
            -- 调用 Bot 提供的 API 发送消息
            bot:send_group_msg(event.group_id, "你好呀！")
        end
    end)
end
